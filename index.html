<!DOCTYPE html>
<html>
<head>
<title>Game</title>
<style>
  body {
    margin: 0;
    padding: 0;
    /* Optional: background color for the page */
    background-color: #f0f0f0;
  }
  /* Ensure the game container itself doesn't introduce unwanted spacing */
  #game-container {
    padding: 0;
    margin: 0;
    display: flex; /* Helps in centering if Phaser doesn't fill screen */
    justify-content: center; /* Centers canvas horizontally */
    align-items: center; /* Centers canvas vertically */
    width: 100vw;
    height: 100vh;
  }
</style>
</head>
<body>
  <div id="game-container"></div>
  <script src="https://cdn.jsdelivr.net/npm/phaser@3.60.0/dist/phaser.min.js"></script>
  <script src="game.js"></script>
</body>
</html>
