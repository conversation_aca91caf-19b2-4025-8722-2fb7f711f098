// src/ui/StatsDisplay.js
// Phaser is loaded globally from CDN

class StatsDisplay {
    constructor(scene, player) {
        this.scene = scene;
        this.player = player;

        // Create Phaser text objects for health, trust, and crime record
        this.healthText = scene.add.text(10, 10, `Health: ${player.health}`, { fontSize: '16px', fill: '#000' });
        this.trustText = scene.add.text(10, 30, `Trust: ${player.trust}`, { fontSize: '16px', fill: '#000' });
        this.crimeRecordText = scene.add.text(10, 50, `Crime: ${player.crimeRecord}`, { fontSize: '16px', fill: '#000' });
        this.inventoryCountText = scene.add.text(10, 70, `Items: ${player.inventory.length}`, { fontSize: '16px', fill: '#000' });

        // Apply setScrollFactor(0) to each text object
        this.healthText.setScrollFactor(0);
        this.trustText.setScrollFactor(0);
        this.crimeRecordText.setScrollFactor(0);
        this.inventoryCountText.setScrollFactor(0);
    }

    update() {
        // Update the text of each UI element to reflect the current player stats
        this.healthText.setText(`Health: ${this.player.health}`);
        this.trustText.setText(`Trust: ${this.player.trust}`);
        this.crimeRecordText.setText(`Crime: ${this.player.crimeRecord}`);
        this.inventoryCountText.setText(`Items: ${this.player.inventory.length}`);
    }
}

export default StatsDisplay;
