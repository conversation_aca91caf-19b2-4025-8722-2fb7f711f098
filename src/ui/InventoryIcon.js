// src/ui/InventoryIcon.js
// Phaser is loaded globally from CDN
import { SVGAssets } from '../configs/AssetConfig.js'; // For default size

class InventoryIcon extends Phaser.GameObjects.Sprite {
    constructor(scene, x, y, player) {
        super(scene, x, y, 'ui_inventory'); // Key from AssetConfig.js

        this.player = player;
        scene.add.existing(this);
        this.setInteractive();
        this.setScrollFactor(0); // Keep it fixed on the screen

        // Set display size (using dimensions from AssetConfig if available, otherwise default)
        const iconDimensions = SVGAssets.ui_inventory || { width: 24, height: 24 };
        this.setDisplaySize(iconDimensions.width * 1.25, iconDimensions.height * 1.25); // Slightly larger for clickability

        this.on('pointerdown', () => {
            console.log('Inventory Clicked! Current items: ' + this.player.inventory.join(', '));
            // Future: this would toggle a full inventory panel UI
        });
    }
}

export default InventoryIcon;
