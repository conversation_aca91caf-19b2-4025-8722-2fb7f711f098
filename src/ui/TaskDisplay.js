// src/ui/TaskDisplay.js
// Phaser is loaded globally from CDN

class TaskDisplay {
    constructor(scene, x, y, questSystem) {
        this.scene = scene;
        this.questSystem = questSystem; // Store QuestSystem reference
        this.taskTextElement = scene.add.text(
            x,
            y,
            'Current Task: None',
            {
                fontSize: '16px',
                fill: '#000',
                backgroundColor: 'rgba(255,255,255,0.7)',
                padding: {x:5, y:3}
            }
        );
        this.taskTextElement.setScrollFactor(0);
    }

    update() { // Renamed from updateTask and logic changed
        const currentObjective = this.questSystem.getCurrentObjective();
        this.taskTextElement.setText(`Current Task: ${currentObjective}`);
    }
}

export default TaskDisplay;
