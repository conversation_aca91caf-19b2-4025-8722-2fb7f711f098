// src/ui/DialogueBox.js
import Phaser from 'phaser';

class DialogueBox extends Phaser.GameObjects.Container {
    constructor(scene) {
        const x = scene.cameras.main.width / 2;
        const y = scene.cameras.main.height - 100; // Positioned at bottom-center
        super(scene, x, y);

        this.scene = scene;
        this.currentSpeaker = null;
        this.lines = [];
        this.currentLineIndex = 0;

        // Background
        this.dialogueBg = scene.add.graphics();
        this.dialogueBg.fillStyle(0x000000, 0.8);
        // Relative to container's origin (x,y) which is now the center of the box
        this.dialogueBg.fillRect(-250, -40, 500, 80);
        this.add(this.dialogueBg);

        // Speaker's Name Text
        this.nameText = scene.add.text(-240, -30, '', {
            fontSize: '16px',
            fill: '#ffff00', // Yellow for name
            fontStyle: 'bold'
        });
        this.add(this.nameText);

        // Dialogue Content Text
        this.dialogueText = scene.add.text(-240, -10, '', {
            fontSize: '16px',
            fill: '#ffffff',
            wordWrap: { width: 480 },
            lineSpacing: 4
        });
        this.add(this.dialogueText);

        // Next/Close Button
        this.nextButton = scene.add.text(230, 25, 'Next >', {
            fontSize: '16px',
            fill: '#00ff00', // Green for next
            backgroundColor: '#333333',
            padding: {x:5, y:2}
        }).setOrigin(1, 0).setInteractive(); // Origin bottom-right of text box
        this.add(this.nextButton);
        this.nextButton.on('pointerdown', this.advanceDialogue, this);

        this.setVisible(false);
        scene.add.existing(this); // Add container to the scene
        this.setScrollFactor(0).setDepth(100); // Ensure it's on top and doesn't scroll
    }

    startDialogue(speakerName, dialogueLines) {
        if (!dialogueLines || dialogueLines.length === 0) {
            console.warn('DialogueBox: Attempted to start dialogue with no lines.');
            return;
        }
        this.currentSpeaker = speakerName;
        this.lines = [...dialogueLines];
        this.currentLineIndex = 0;

        this.nameText.setText(this.currentSpeaker);
        this.displayCurrentLine();
        this.setVisible(true);
    }

    displayCurrentLine() {
        if (this.currentLineIndex < this.lines.length) {
            this.dialogueText.setText(this.lines[this.currentLineIndex]);
            if (this.currentLineIndex >= this.lines.length - 1) {
                this.nextButton.setText('Close');
            } else {
                this.nextButton.setText('Next >');
            }
        }
    }

    advanceDialogue() {
        this.currentLineIndex++;
        if (this.currentLineIndex >= this.lines.length) {
            this.closeDialogue();
        } else {
            this.displayCurrentLine();
        }
    }

    closeDialogue() {
        this.setVisible(false);
        this.currentSpeaker = null;
        this.lines = [];
        this.currentLineIndex = 0;
        // Future: could emit an event 'dialogueClosed'
        // if (this.scene.inputManager) this.scene.inputManager.dialogueActive = false;
        console.log('Dialogue closed.');
    }
}

export default DialogueBox;
