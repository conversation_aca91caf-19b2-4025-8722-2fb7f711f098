// src/main.js
// Phaser is loaded globally from CDN in index.html
import { AssetLoaderScene } from './scenes/AssetLoaderScene.js';
import MainMenuScene from './scenes/MainMenuScene.js';
import Floor1Reception from './scenes/Floor1Reception.js';
import CombatScene from './scenes/CombatScene.js'; // Import CombatScene
import Floor1Office from './scenes/Floor1Office.js'; // Import Floor1Office

const config = {
    type: Phaser.AUTO,
    width: 800,
    height: 600,
    parent: 'game-container', // Matches the div id in index.html
    physics: {
        default: 'arcade',
        arcade: {
            // gravity: { y: 0 }, // No gravity needed for top-down
            debug: false // Set to true for debugging physics bodies
        }
    },
    scene: [AssetLoaderScene, MainMenuScene, Floor1Reception, Floor1Office, CombatScene] // Add Floor1Office
};

// Create a new Phaser game instance
const game = new Phaser.Game(config);

console.log('Phaser Game initialized from src/main.js');
