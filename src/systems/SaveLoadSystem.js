// src/systems/SaveLoadSystem.js

class SaveLoadSystem {
    constructor(scenePlugin, player, questSystem) {
        // scenePlugin is expected to be scene.scene (Phaser's ScenePlugin)
        this.scenePlugin = scenePlugin;
        this.player = player;
        this.questSystem = questSystem;
        this.saveGameKey = 'escapeNorthMyanmarSaveData';
        console.log('SaveLoadSystem initialized.');
    }

    saveGame() {
        console.log('Attempting to save game...');

        const activeSceneKey = this.getActiveSceneKey();
        if (!activeSceneKey) {
            console.error('Could not determine active scene key. Aborting save.');
            return;
        }

        const saveData = {
            timestamp: new Date().toISOString(),
            // Player Data
            player: {
                health: this.player.health,
                trust: this.player.trust,
                crimeRecord: this.player.crimeRecord,
                inventory: this.player.inventory,
                psychologicalState: this.player.psychologicalState,
                currentSceneKey: activeSceneKey, // Use the determined active scene key
                x: this.player.x,
                y: this.player.y
            },
            // Quest Data
            quests: this.questSystem.quests, // Save the entire quests object
            // Future: Game Time, NPC states, etc.
        };

        try {
            localStorage.setItem(this.saveGameKey, JSON.stringify(saveData));
            console.log('Game saved successfully to localStorage.', saveData);
        } catch (error) {
            console.error('Error saving game to localStorage:', error);
        }
    }

    getActiveSceneKey() {
        // The player's current scene is the most reliable way to get the current playable scene key.
        if (this.player && this.player.scene) {
            return this.player.scene.scene.key;
        }
        // Fallback or more complex logic if needed (e.g., iterating scene plugin's active scenes)
        // For now, relying on player.scene.
        console.warn('SaveLoadSystem: Player or player.scene not available to get active scene key.');
        return null;
    }

    loadGame() {
        // Placeholder for now - will be implemented in the next subtask
        console.log('loadGame() called - to be implemented.');
        const data = localStorage.getItem(this.saveGameKey);
        if (data) {
            try {
                const saveData = JSON.parse(data);
                console.log('Save data found:', saveData);
                return saveData;
            } catch (error) {
                console.error('Error parsing save data from localStorage:', error);
                return null;
            }
        }
        return null;
    }

    hasSaveData() {
        return localStorage.getItem(this.saveGameKey) !== null;
    }
}

export default SaveLoadSystem;
