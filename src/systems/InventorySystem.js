// src/systems/InventorySystem.js
import Phaser from 'phaser'; // Included for good practice, might not be strictly needed for pure logic

class InventorySystem {
    constructor(player) {
        this.player = player; // The inventory array itself is on the Player object
    }

    addItem(itemId) {
        // Assumes Player.js has an addItem method that handles the array
        // and any other player-specific logic related to adding an item.
        if (typeof this.player.addItem === 'function') {
            this.player.addItem(itemId);
        } else {
            // Fallback if Player.js doesn't have a dedicated addItem method
            this.player.inventory.push(itemId);
            console.warn('InventorySystem: Player.addItem() method not found. Added directly to player.inventory.');
        }

        console.log(`InventorySystem: Added ${itemId}. Current inventory: ${this.player.inventory.join(', ')}`);
        // Future: could emit an event like 'inventoryChanged'
        // this.scene.events.emit('inventoryChanged', this.player.inventory);
    }

    hasItem(itemId) {
        return this.player.inventory.includes(itemId);
    }

    // Optional: removeItem, if needed later
    // removeItem(itemId) {
    //     const index = this.player.inventory.indexOf(itemId);
    //     if (index > -1) {
    //         this.player.inventory.splice(index, 1);
    //         console.log(`InventorySystem: Removed ${itemId}. Current inventory: ${this.player.inventory.join(', ')}`);
    //         // Future: emit event
    //         return true;
    //     }
    //     return false;
    // }
}

export default InventorySystem;
