// src/systems/TimeSystem.js
// Phaser is loaded globally from CDN

class TimeSystem {
    constructor(scene) {
        this.scene = scene; // Scene reference, not used in this version but good for future
        this.gameHours = 8; // Start at 8 AM
        this.gameMinutes = 0;
        this.secondsPerGameMinute = 2; // How many real seconds for one game minute to pass
        this.elapsedRealTime = 0; // Accumulator for real time

        console.log(`TimeSystem initialized. Starting at ${this.getFormattedTime()}.`);
    }

    update(delta) { // delta is time in ms since last frame
        this.elapsedRealTime += delta / 1000; // Convert delta to seconds and accumulate

        if (this.elapsedRealTime >= this.secondsPerGameMinute) {
            const minutesPassed = Math.floor(this.elapsedRealTime / this.secondsPerGameMinute);
            this.elapsedRealTime -= minutesPassed * this.secondsPerGameMinute; // Subtract accumulated time

            this.gameMinutes += minutesPassed;

            while (this.gameMinutes >= 60) {
                this.gameMinutes -= 60;
                this.gameHours++;
                if (this.gameHours >= 24) {
                    this.gameHours = 0; // Midnight rollover
                }
            }
            // console.log(`Game time: ${this.getFormattedTime()}`); // Optional: log time changes
        }
    }

    getFormattedTime() {
        const hoursStr = String(this.gameHours).padStart(2, '0');
        const minutesStr = String(this.gameMinutes).padStart(2, '0');
        return `${hoursStr}:${minutesStr}`;
    }
}

export default TimeSystem;
