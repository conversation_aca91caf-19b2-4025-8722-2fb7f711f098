// src/systems/QuestSystem.js
import { QuestsConfig } from '../configs/QuestsConfig.js';

class QuestSystem {
    constructor() {
        this.quests = JSON.parse(JSON.stringify(QuestsConfig)); // Deep copy
        // activeQuests and completedQuests arrays are not strictly needed if isStarted/isCompleted flags are in this.quests
        console.log('QuestSystem initialized with quests:', this.quests);
    }

    startQuest(questId) {
        if (this.quests[questId] && !this.quests[questId].isStarted) {
            this.quests[questId].isStarted = true;
            console.log(`Quest started: ${this.quests[questId].title}`);
            // Future: Emit event 'questStarted', questId
            return this.quests[questId];
        } else {
            console.log(`Quest ${questId} already started or does not exist.`);
            return null;
        }
    }

    getActiveQuest() {
        // For simplicity, assume one active quest at a time, or the first uncompleted started one.
        for (const questId in this.quests) {
            const quest = this.quests[questId];
            if (quest.isStarted && !quest.isCompleted) {
                return quest;
            }
        }
        return null; // No active quest
    }

    getCurrentObjective() {
        const activeQuest = this.getActiveQuest();
        if (activeQuest) {
            for (const stage of activeQuest.stages) {
                if (!stage.isCompleted) { return stage.objective; }
            }
        }
        return "None";
    }

    completeObjective(questId, stageIndex) {
        const quest = this.quests[questId];
        if (quest && quest.isStarted && quest.stages[stageIndex] && !quest.stages[stageIndex].isCompleted) {
            quest.stages[stageIndex].isCompleted = true;
            console.log(`Objective completed for ${questId}: ${quest.stages[stageIndex].objective}`);

            const allStagesDone = quest.stages.every(s => s.isCompleted);
            if (allStagesDone) {
                this.completeQuest(questId);
            }
            // Future: Emit event 'objectiveCompleted', questId, stageIndex
        }
    }

    completeQuest(questId) {
        const quest = this.quests[questId];
        if (quest && quest.isStarted && !quest.isCompleted) {
            quest.isCompleted = true;
            console.log(`Quest completed: ${quest.title}`);
            // Future: Grant rewards, emit event 'questCompleted', questId
        }
    }

    // getQuestStatus(questId) - Can be refined later if needed
    // checkQuestProgress(questId) - Can be added later
    // getActiveQuests() - Could return an array if multiple active quests are allowed
}

export default QuestSystem;
