// src/scenes/Floor1Office.js
// Phaser is loaded globally from CDN
import Player from '../prefabs/Player.js';
import { LevelsConfig } from '../configs/LevelsConfig.js';
import StatsDisplay from '../ui/StatsDisplay.js';
import InventoryIcon from '../ui/InventoryIcon.js';
import TaskDisplay from '../ui/TaskDisplay.js';
import NPC from '../prefabs/NPC.js'; // Import NPC
import InteractiveItem from '../prefabs/InteractiveItem.js'; // Import InteractiveItem
import InventorySystem from '../systems/InventorySystem.js'; // Import InventorySystem
import DialogueBox from '../ui/DialogueBox.js'; // Import DialogueBox
import QuestSystem from '../systems/QuestSystem.js'; // Import QuestSystem

class Floor1Office extends Phaser.Scene {
    constructor() {
        super('Floor1Office');
        // Initialize levelConfig here, but it will be properly set if entry exists in LevelsConfig
        this.levelConfig = LevelsConfig.Floor1Office || {
            backgroundColor: '#D0D0F0',
            playerStartPosition: { x: 100, y: 100 },
            displayName: 'Floor 1: Office (Default)'
        };
    }

    init(data) {
        this.playerData = data;
        console.log('Floor1Office init data:', data);
    }

    create() {
        this.cameras.main.setBackgroundColor(this.levelConfig.backgroundColor);

        this.add.text(10, 10, this.levelConfig.displayName, {
            fontSize: '20px',
            fill: '#000' // Assuming black text for this theme
        }).setScrollFactor(0);

        const playerX = (this.playerData && typeof this.playerData.startX !== 'undefined') ? this.playerData.startX : this.levelConfig.playerStartPosition.x;
        const playerY = (this.playerData && typeof this.playerData.startY !== 'undefined') ? this.playerData.startY : this.levelConfig.playerStartPosition.y;
        this.player = new Player(this, playerX, playerY);

        if (this.playerData && typeof this.playerData.playerHealth !== 'undefined') {
            this.player.health = this.playerData.playerHealth;
        }
        if (this.playerData && this.playerData.inventory) {
            this.player.inventory = this.playerData.inventory;
        }

        // UI Elements
        this.statsDisplay = new StatsDisplay(this, this.player);
        this.inventoryIcon = new InventoryIcon(this, this.cameras.main.width - 40, 10, this.player);

        // Instantiate QuestSystem first as TaskDisplay depends on it
        this.questSystem = new QuestSystem();
        this.taskDisplay = new TaskDisplay(this, this.cameras.main.centerX, 10, this.questSystem); // Pass questSystem
        this.taskDisplay.taskTextElement.setOrigin(0.5, 0);

        // Instantiate InventorySystem if not already (e.g. if player is created fresh here without full state transfer)
        // This is more robust if this scene can be started without full player state from another scene.
        if (!this.inventorySystem) {
            this.inventorySystem = new InventorySystem(this.player);
        }

        // Instantiate DialogueBox
        this.dialogueBox = new DialogueBox(this);

        // Add New NPC
        const sarahDialogue = ["Oh, hello there.", "This office is usually quite busy.", "Have you seen my stapler?"];
        this.officeWorkerSarah = new NPC(this, 200, 250, 'npc002', 'Sarah', 'npc_female_1', sarahDialogue);

        // Add New Collectible Item
        this.collectibleMemo = new InteractiveItem(this, 400, 300, 'item_office_memo');
        this.physics.add.overlap(this.player, this.collectibleMemo, this.handleCollectItem, null, this);


        // Click-to-move
        this.input.on('pointerdown', (pointer) => {
            if (this.player) {
                if (this.dialogueBox && this.dialogueBox.visible) {
                    return; // Don't move player if dialogue is active
                }
                // Basic check to prevent moving if UI is clicked
                if (this.inventoryIcon.getBounds().contains(pointer.x, pointer.y)) {
                    return;
                }
                if (this.officeWorkerSarah && this.officeWorkerSarah.getBounds().contains(pointer.x, pointer.y)) {
                    // NPC's own talk method should stopPropagation.
                    return;
                }
                // Add more checks for other UI elements if necessary
                this.player.moveTo(pointer.x, pointer.y);
            }
        }, this);

        // Temporary "Back to Reception" door
        const backDoor = this.add.text(700, 550, 'To Reception', {
            fontSize: '18px',
            fill: '#fff',
            backgroundColor: '#333',
            padding: {x:10, y:5}
        }).setOrigin(0.5).setInteractive();

        backDoor.on('pointerdown', (pointer) => {
            pointer.event.stopPropagation();
            this.scene.start('Floor1Reception', {
                playerHealth: this.player.health,
                inventory: this.player.inventory,
                startX: 650, // Example: player appears near where the office door was in reception
                startY: 150
            });
        }, this);

        // Ensure backDoor doesn't move player on click
        this.input.on('gameobjectdown', (pointer, gameObject) => {
            if (gameObject === backDoor) {
                // Event is already handled by backDoor's own pointerdown,
                // but stopPropagation can be good practice if there were other global handlers.
                // pointer.event.stopPropagation(); already done in backDoor's handler
            }
        }, this);
    }

    update(time, delta) {
        if (!this.player) return;

        // Player movement
        const speed = 200;
        this.physics.moveTo(this.player, this.player.targetX, this.player.targetY, speed);
        const distance = Phaser.Math.Distance.Between(
            this.player.x, this.player.y,
            this.player.targetX, this.player.targetY
        );
        if (distance < 4) {
            this.player.body.reset(this.player.targetX, this.player.targetY);
        }

        // Update UI
        this.statsDisplay.update();
        this.taskDisplay.update(); // Update task display
    }

    handleCollectItem(player, itemObject) {
        // Ensure inventory system exists. It should if player is passed correctly or initialized here.
        if (!this.inventorySystem) {
            console.warn('InventorySystem not found in Floor1Office. Initializing.');
            this.inventorySystem = new InventorySystem(this.player);
        }
        const itemId = itemObject.collect();
        if (itemId) {
            this.inventorySystem.addItem(itemId);
        }
        // StatsDisplay will update inventory count automatically as it reads player.inventory.length
    }
}

export default Floor1Office;
