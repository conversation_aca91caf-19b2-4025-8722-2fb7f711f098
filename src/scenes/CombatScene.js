// src/scenes/CombatScene.js
// Phaser is loaded globally from CDN

class CombatScene extends Phaser.Scene {
    constructor() {
        super('CombatScene');
    }

    init(data) {
        this.player = data.player; // Reference to the player object
        this.enemyId = data.enemyId;
        this.originSceneKey = data.previousSceneKey;

        this.enemyHealth = 50; // Placeholder enemy health
        this.enemyAttack = 5;  // Placeholder enemy attack power
        this.isPlayerTurn = true;

        console.log(`CombatScene init. Player health: ${this.player.health}, Enemy: ${this.enemyId}, From: ${this.originSceneKey}`);
    }

    create() {
        this.cameras.main.setBackgroundColor('#800000'); // Dark Red

        // Display Texts
        this.add.text(this.cameras.main.centerX, 50, `Combat vs: ${this.enemyId}`, { fontSize: '24px', fill: '#fff' }).setOrigin(0.5);
        this.playerHealthText = this.add.text(this.cameras.main.centerX, 100, `Player Health: ${this.player.health}`, { fontSize: '20px', fill: '#fff' }).setOrigin(0.5);
        this.enemyHealthText = this.add.text(this.cameras.main.width - 50, 100, `Enemy Health: ${this.enemyHealth}`, { fontSize: '18px', fill: '#fff' }).setOrigin(1, 0.5); // Top-right

        // Buttons
        const buttonStyle = { fontSize: '20px', fill: '#fff', backgroundColor: '#555', padding: {x:10,y:5} };

        this.attackButton = this.add.text(this.cameras.main.centerX - 100, this.cameras.main.height - 150, 'Attack', buttonStyle).setOrigin(0.5).setInteractive();
        this.attackButton.on('pointerdown', this.playerAttack, this);

        this.itemButton = this.add.text(this.cameras.main.centerX + 100, this.cameras.main.height - 150, 'Use Item', buttonStyle).setOrigin(0.5).setInteractive();
        this.itemButton.on('pointerdown', () => { console.log('Use Item clicked - not implemented yet.'); }, this);

        this.fleeButton = this.add.text(this.cameras.main.width - 80, this.cameras.main.height - 50, 'Flee', buttonStyle).setOrigin(0.5).setInteractive();
        this.fleeButton.on('pointerdown', () => {
            console.log('Fleeing combat.');
            this.scene.stop('CombatScene');
            this.scene.resume(this.originSceneKey);
        }, this);

        // Turn Indicator
        this.turnIndicatorText = this.add.text(this.cameras.main.centerX, this.cameras.main.height - 50, "Player's Turn", { fontSize: '20px', fill: '#fff' }).setOrigin(0.5);

        this.updateCombatUI(); // Set initial button states
    }

    updateCombatUI() {
        this.playerHealthText.setText(`Player Health: ${this.player.health}`);
        this.enemyHealthText.setText(`Enemy Health: ${this.enemyHealth}`);

        if (this.isPlayerTurn) {
            this.attackButton.setAlpha(1).setInteractive(true);
            this.itemButton.setAlpha(1).setInteractive(true);
            this.turnIndicatorText.setText("Player's Turn");
        } else {
            this.attackButton.setAlpha(0.5).disableInteractive();
            this.itemButton.setAlpha(0.5).disableInteractive();
            this.turnIndicatorText.setText("Enemy's Turn");
        }
    }

    playerAttack() {
        if (!this.isPlayerTurn) return;
        console.log('Player attacks!');
        const playerAttackPower = 10; // Placeholder
        this.enemyHealth -= playerAttackPower;
        if (this.enemyHealth < 0) this.enemyHealth = 0;

        this.updateCombatUI();

        if (this.enemyHealth <= 0) {
            this.winCombat();
            return;
        }

        this.isPlayerTurn = false;
        this.updateCombatUI(); // To disable buttons and update turn text
        this.time.delayedCall(1000, this.enemyAttackTurn, [], this);
    }

    enemyAttackTurn() {
        if (this.isPlayerTurn || this.player.health <= 0 || this.enemyHealth <=0) return;
        console.log('Enemy attacks!');
        this.player.health -= this.enemyAttack;
        if (this.player.health < 0) this.player.health = 0;

        this.updateCombatUI();

        if (this.player.health <= 0) {
            this.loseCombat();
            return;
        }

        this.isPlayerTurn = true;
        this.updateCombatUI(); // To re-enable buttons and update turn text
    }

    winCombat() {
        console.log('Player wins combat!');
        this.add.text(this.cameras.main.centerX, this.cameras.main.centerY, 'YOU WIN!', { fontSize: '32px', fill: '#00ff00' }).setOrigin(0.5);
        this.attackButton.disableInteractive().setAlpha(0.5);
        this.itemButton.disableInteractive().setAlpha(0.5);
        this.fleeButton.disableInteractive().setAlpha(0.5); // Also disable flee on win/loss
        this.turnIndicatorText.setVisible(false);
        this.time.delayedCall(2000, () => { this.scene.stop('CombatScene'); this.scene.resume(this.originSceneKey); }, [], this);
    }

    loseCombat() {
        console.log('Player loses combat! Game Over (for now).');
        this.add.text(this.cameras.main.centerX, this.cameras.main.centerY, 'YOU LOSE!', { fontSize: '32px', fill: '#ff0000' }).setOrigin(0.5);
        this.attackButton.disableInteractive().setAlpha(0.5);
        this.itemButton.disableInteractive().setAlpha(0.5);
        this.fleeButton.disableInteractive().setAlpha(0.5);
        this.turnIndicatorText.setVisible(false);
        this.time.delayedCall(2000, () => { this.scene.stop('CombatScene'); this.scene.resume(this.originSceneKey); }, [], this);
    }
}

export default CombatScene;
