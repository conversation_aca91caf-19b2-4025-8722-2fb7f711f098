// src/scenes/MainMenuScene.js
// Phaser is loaded globally from CDN

class MainMenuScene extends Phaser.Scene {
    constructor() {
        super('MainMenuScene');
    }

    create() {
        this.cameras.main.setBackgroundColor('#4f4f4f'); // Dark gray background

        this.add.text(
            this.cameras.main.centerX,
            this.cameras.main.centerY - 100,
            'Escape from North Myanmar',
            { fontSize: '32px', fill: '#fff' }
        ).setOrigin(0.5);

        const startButton = this.add.text(
            this.cameras.main.centerX,
            this.cameras.main.centerY,
            'Start Game',
            {
                fontSize: '24px',
                fill: '#fff',
                backgroundColor: '#1f1f1f',
                padding: { x: 10, y: 5 }
            }
        ).setOrigin(0.5).setInteractive();

        startButton.on('pointerdown', () => {
            this.scene.start('Floor1Reception');
        });
    }
}

export default MainMenuScene;
