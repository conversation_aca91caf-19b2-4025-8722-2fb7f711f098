// src/scenes/Floor1Reception.js
// Phaser is loaded globally from CDN
import Player from '../prefabs/Player.js';
import { LevelsConfig } from '../configs/LevelsConfig.js';
import StatsDisplay from '../ui/StatsDisplay.js';
import InventoryIcon from '../ui/InventoryIcon.js';
import InventorySystem from '../systems/InventorySystem.js';
import InteractiveItem from '../prefabs/InteractiveItem.js';
import NPC from '../prefabs/NPC.js';
import QuestSystem from '../systems/QuestSystem.js';
import TaskDisplay from '../ui/TaskDisplay.js';
import InteractionManager from '../systems/InteractionManager.js';
import { SVGAssets } from '../configs/AssetConfig.js'; // For computer sprite size
import Enemy from '../prefabs/Enemy.js';
import TimeSystem from '../systems/TimeSystem.js';
import DialogueBox from '../ui/DialogueBox.js'; // Import DialogueBox
import SaveLoadSystem from '../systems/SaveLoadSystem.js'; // Import SaveLoadSystem

class Floor1Reception extends Phaser.Scene {
    constructor() {
        super('Floor1Reception');
        this.player = null; // To store the player instance
        this.levelConfig = LevelsConfig.Floor1Reception; // Store specific level config
    }

    init(data) {
        // Store any data passed from the previous scene (e.g., player state)
        this.playerData = data;
        console.log('Floor1Reception initialized with data:', data);
    }

    create() {
        this.cameras.main.setBackgroundColor(this.levelConfig.backgroundColor);

        this.add.text(10, 10, this.levelConfig.displayName, {
            fontSize: '20px',
            fill: '#000' // Assuming black text is desired for this theme
        }).setScrollFactor(0); // Fix text to camera

        // Instantiate the Player prefab
        // Use data passed from init if available, otherwise use level config's start position
        const playerX = (this.playerData && this.playerData.startX) ? this.playerData.startX : this.levelConfig.playerStartPosition.x;
        const playerY = (this.playerData && this.playerData.startY) ? this.playerData.startY : this.levelConfig.playerStartPosition.y;
        this.player = new Player(this, playerX, playerY);
        this.statsDisplay = new StatsDisplay(this, this.player); // Instantiate StatsDisplay

        // Instantiate Inventory System and UI Icon
        this.inventorySystem = new InventorySystem(this.player);
        this.inventoryIcon = new InventoryIcon(this, this.cameras.main.width - 40, 10, this.player);

        // Add a collectible item
        this.collectibleKeyCard = new InteractiveItem(this, 200, 300, 'item_key_card');
        this.physics.add.overlap(this.player, this.collectibleKeyCard, this.handleCollectItem, null, this);

        // Instantiate an NPC
        const bobDialogue = ["Hello, I'm Bob.", "Welcome to this place.", "Be careful around here."];
        this.testNPC = new NPC(this, 300, 150, 'npc001', 'Bob', 'npc_male_1', bobDialogue);

        // Instantiate Quest System and Task Display
        this.questSystem = new QuestSystem();
        this.taskDisplay = new TaskDisplay(this, this.cameras.main.centerX, 10, this.questSystem); // Pass questSystem
        this.taskDisplay.taskTextElement.setOrigin(0.5, 0); // Center the task display

        // Instantiate Interaction Manager
        this.interactionManager = new InteractionManager(this);

        // Instantiate Time System and UI
        this.timeSystem = new TimeSystem(this);
        this.timeTextDisplay = this.add.text(this.cameras.main.width - 10, 40, `Time: ${this.timeSystem.getFormattedTime()}`, { fontSize: '16px', fill: '#000', align: 'right' });
        this.timeTextDisplay.setOrigin(1, 0); // Align to right
        this.timeTextDisplay.setScrollFactor(0);

        // Instantiate DialogueBox
        this.dialogueBox = new DialogueBox(this);

        // Instantiate SaveLoadSystem
        // Pass this.scene.scene for the sceneManager (ScenePlugin)
        this.saveLoadSystem = new SaveLoadSystem(this.scene.scene, this.player, this.questSystem);

        // Add Save Game Button
        const saveButtonX = this.cameras.main.width - 100; // Example position
        const saveButtonY = this.cameras.main.height - 30; // Example position
        this.saveButton = this.add.text(saveButtonX, saveButtonY, 'Save Game', {
            fontSize: '16px',
            fill: '#fff',
            backgroundColor: '#007bff', // Blue background
            padding: { x: 8, y: 4 }
        }).setOrigin(0.5).setInteractive().setScrollFactor(0);
        this.saveButton.on('pointerdown', () => {
            this.saveLoadSystem.saveGame();
            // Optional: Add visual feedback like "Game Saved!" text briefly
            const feedbackText = this.add.text(saveButtonX, saveButtonY - 30, 'Game Saved!', {fontSize: '14px', fill: '#00ff00'}).setOrigin(0.5).setScrollFactor(0);
            this.time.delayedCall(1500, () => feedbackText.destroy());
        });

        // Add door to office
        this.officeDoor = this.physics.add.sprite(700, 150, 'door').setImmovable(true).setInteractive();
        const doorAssetCfg = SVGAssets['door'];
        if(doorAssetCfg) this.officeDoor.setDisplaySize(doorAssetCfg.width, doorAssetCfg.height);

        this.officeDoor.on('pointerdown', (pointer) => {
            pointer.event.stopPropagation(); // Prevent player moving to door
            console.log('Entering Office...');
            this.scene.start('Floor1Office', {
                playerHealth: this.player.health,
                inventory: this.player.inventory,
                // Optional: specify startX, startY for player in office if door has specific exit point
                // startX: specificX, startY: specificY
            });
        }, this);

        // Event listener for scene resume after combat
        this.events.on('resume', () => {
            console.log(`Resumed scene ${this.scene.key} after combat.`);
            // Player health is a shared reference, StatsDisplay will update it.
            // If the guard's vision cone was hidden, make sure it becomes visible again
            // if combat is over and guard is not "defeated" (not implemented yet).
            if (this.guard && this.guard.visionConeGraphics) {
                // For simplicity, enemy is reset for now if player flees.
                // A real game would need better state management for the enemy.
                this.guard.combatTriggered = false;
                this.guard.visionConeGraphics.setVisible(true);
            }
        });


        // Basic Player Movement Input: Set target on click
        this.input.on('pointerdown', (pointer) => {
            if (this.player) {
                // Ensure clicks on UI elements or NPCs don't move player
                if (this.dialogueBox && this.dialogueBox.visible) {
                    return;
                }
                if (this.inventoryIcon.getBounds().contains(pointer.x, pointer.y)) {
                    return;
                }
                if (this.officeDoor.getBounds().contains(pointer.x, pointer.y)) {
                    return;
                }
                if (this.saveButton && this.saveButton.getBounds().contains(pointer.x, pointer.y)) {
                    return; // Click was on save button
                }
                // NPC's talk method has stopPropagation, so direct click on NPC won't move player.
                this.player.moveTo(pointer.x, pointer.y);
            }
        }, this);
    }

    update(time, delta) {
        if (!this.player) return;

        this.statsDisplay.update(); // Update stats display
        this.taskDisplay.update(); // Update task display

        this.timeSystem.update(delta); // Update the time system
        this.timeTextDisplay.setText(`Time: ${this.timeSystem.getFormattedTime()}`); // Update time display

        const speed = 200; // pixels per second

        // Move player towards targetX, targetY using Arcade Physics
        this.physics.moveTo(this.player, this.player.targetX, this.player.targetY, speed);

        // Stop player if close enough to target to prevent jittering
        const distance = Phaser.Math.Distance.Between(
            this.player.x,
            this.player.y,
            this.player.targetX,
            this.player.targetY
        );

        if (distance < 4) {
            this.player.body.reset(this.player.targetX, this.player.targetY);
        }
    }

    handleCollectItem(player, itemObject) {
        const itemId = itemObject.collect(); // Calls the item's own collect method
        if (itemId) {
            this.inventorySystem.addItem(itemId);
            // StatsDisplay will now update the count via its own update loop
        }
    }
}

export default Floor1Reception;
