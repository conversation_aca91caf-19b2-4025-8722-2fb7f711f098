// src/scenes/AssetLoaderScene.js
import Phaser from 'phaser';
import { SVGAssets } from '../configs/AssetConfig.js';

export class AssetLoaderScene extends Phaser.Scene {
    constructor() {
        super('AssetLoaderScene');
    }

    preload() {
        const centerX = this.cameras.main.width / 2;
        const centerY = this.cameras.main.height / 2;

        // Display a "Loading assets..." text message
        const loadingText = this.add.text(centerX, centerY - 20, 'Loading assets...', {
            fontSize: '24px',
            fill: '#ffffff'
        }).setOrigin(0.5);

        // Optional: Loading bar
        const progressBar = this.add.graphics();
        const progressBox = this.add.graphics();
        progressBox.fillStyle(0x222222, 0.8);
        progressBox.fillRect(centerX - 160, centerY + 20, 320, 50);

        this.load.on('progress', (value) => {
            progressBar.clear();
            progressBar.fillStyle(0xffffff, 1);
            progressBar.fillRect(centerX - 150, centerY + 30, 300 * value, 30);
        });

        // Loop through SVGAssets and load them
        for (const key in SVGAssets) {
            if (Object.hasOwnProperty.call(SVGAssets, key)) {
                const asset = SVGAssets[key];
                this.load.svg(key, asset.path, { width: asset.width, height: asset.height });
            }
        }

        // Listen for load completion
        this.load.on('complete', () => {
            loadingText.setText('Load Complete!');
            progressBar.destroy();
            progressBox.destroy();
            // Transition to the next scene (MainMenuScene will be created in a future step)
            console.log('Asset loading complete. Transitioning to MainMenuScene (placeholder).');
            this.scene.start('MainMenuScene');
        });
    }

    create() {
        // This method can be used to refine loading text visuals or other elements if needed
        // For now, preload handles most of the logic.
        console.log('AssetLoaderScene create method called.');
    }
}
