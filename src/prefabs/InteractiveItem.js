// src/prefabs/InteractiveItem.js
import Phaser from 'phaser';
import { ItemsConfig } from '../configs/ItemsConfig.js';
import { SVGAssets } from '../configs/AssetConfig.js'; // For default size

class InteractiveItem extends Phaser.GameObjects.Sprite {
    constructor(scene, x, y, itemId) {
        const itemConfig = ItemsConfig[itemId];
        if (!itemConfig) {
            console.error(`InteractiveItem: Config for item ID '${itemId}' not found.`);
            // Optionally, you could load a default 'error' texture or simply not render
            super(scene, x, y, 'player'); // Fallback to a known texture or handle error
            scene.add.existing(this);
            this.setActive(false).setVisible(false); // Hide if item config is missing
            return;
        }

        super(scene, x, y, itemConfig.assetKey);

        this.itemId = itemId;
        this.itemConfig = itemConfig;

        scene.add.existing(this);
        scene.physics.add.existing(this);
        this.setInteractive(); // For potential direct click interactions later

        // Set display size using dimensions from AssetConfig for the specific assetKey
        const assetDimensions = SVGAssets[itemConfig.assetKey] || { width: 30, height: 30 }; // Default if not in AssetConfig
        this.setDisplaySize(assetDimensions.width, assetDimensions.height);
    }

    collect() {
        console.log(`Collected: ${this.itemConfig.name}`);
        this.disableBody(true, true); // Disable physics and hide
        return this.itemId; // Return the item's ID
    }
}

export default InteractiveItem;
