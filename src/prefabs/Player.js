// src/prefabs/Player.js
// Phaser is loaded globally from CDN
import { SVGAssets } from '../configs/AssetConfig.js'; // For default size

class Player extends Phaser.GameObjects.Sprite {
    constructor(scene, x, y) {
        super(scene, x, y, 'player'); // 'player' is the texture key

        // Add to scene and physics
        scene.add.existing(this);
        scene.physics.add.existing(this);
        this.setCollideWorldBounds(true); // Prevent moving out of screen

        // Initialize player state variables
        this.health = 100;
        this.trust = 0;
        this.crimeRecord = 0;
        this.inventory = []; // Array to hold item IDs or objects
        this.psychologicalState = 'Stable'; // Example state

        // Set display size (using dimensions from AssetConfig if available, otherwise default)
        const playerDimensions = SVGAssets.player || { width: 50, height: 50 };
        this.setDisplaySize(playerDimensions.width, playerDimensions.height);

        // Movement state (target position)
        this.targetX = x;
        this.targetY = y;
    }

    moveTo(x, y) {
        this.targetX = x;
        this.targetY = y;
        // The actual movement logic (e.g., using scene.physics.moveTo)
        // will typically be called from the scene's update loop based on input.
        // For example, in the scene:
        // if (this.player.targetX !== this.player.x || this.player.targetY !== this.player.y) {
        //     this.physics.moveTo(this.player, this.player.targetX, this.player.targetY, 200); // 200 is speed
        // }
    }

    // preUpdate(time, delta) {
    //     super.preUpdate(time, delta);
    //     // This is an alternative place for movement logic if the player moves itself.
    //     // However, for click-to-move, usually the scene drives it via physics.moveTo or direct position setting.
    // }

    addItem(itemId) {
        this.inventory.push(itemId);
        console.log(`Item added: ${itemId}. Inventory:`, this.inventory);
    }

    updateHealth(amount) {
        this.health += amount;
        if (this.health < 0) {
            this.health = 0;
        }
        if (this.health > 100) {
            this.health = 100;
        }
        console.log(`Health updated by ${amount}. Current health: ${this.health}`);
    }

    // Placeholder for other potential player-specific methods
    updatePsychologicalState(newState) {
        this.psychologicalState = newState;
        console.log(`Psychological state updated to: ${this.psychologicalState}`);
    }
}

export default Player;
