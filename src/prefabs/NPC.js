// src/prefabs/NPC.js
// Phaser is loaded globally from CDN
import { SVGAssets } from '../configs/AssetConfig.js'; // For default size

class NPC extends Phaser.GameObjects.Sprite {
    constructor(scene, x, y, npcId, name, assetKey, dialogue = []) {
        super(scene, x, y, assetKey);

        this.scene = scene; // Store scene reference
        this.npcId = npcId;
        this.dialogueLines = dialogue; // Store dialogue lines
        this.npcName = name || 'Unnamed NPC';
        this.questSystem = scene.questSystem; // Give NPC a reference to the quest system

        scene.add.existing(this);
        scene.physics.add.existing(this);
        this.setImmovable(true); // So player can't push it
        this.setInteractive();

        // Set display size using dimensions from AssetConfig for the specific assetKey
        const assetDimensions = SVGAssets[assetKey] || { width: 50, height: 50 }; // Default if not in AssetConfig
        this.setDisplaySize(assetDimensions.width, assetDimensions.height);

        this.on('pointerdown', this.talk, this);
    }

    talk(pointer) {
        if (pointer) {
            pointer.event.stopPropagation(); // Prevent click from passing to scene if NPC is clicked
        }
        console.log(`NPC ${this.npcName} (ID: ${this.npcId}) is talking...`);
        let dialogueToShow = this.dialogueLines; // Default dialogue

        // Quest-specific interaction logic for Bob (npc001)
        if (this.npcId === 'npc001') { // Bob
            const quest = this.questSystem.quests['Q001_MeetAndGreet'];
            if (quest) { // Ensure quest exists
                if (!quest.isStarted) {
                    this.questSystem.startQuest('Q001_MeetAndGreet');
                    // Assuming startQuest also implies talking to Bob is the first step or an immediate consequence
                    this.questSystem.completeObjective('Q001_MeetAndGreet', 0);
                    dialogueToShow = ["Welcome! I need you to meet Sarah in the office.", "She's waiting for you."];
                } else if (quest.stages[0].isCompleted && !quest.stages[1].isCompleted) {
                    dialogueToShow = ["You should go find Sarah in the office area."];
                } else if (quest.isCompleted) {
                    dialogueToShow = ["Thanks for helping out!"];
                }
            }
        }
        // Quest-specific interaction logic for Sarah (npc002)
        else if (this.npcId === 'npc002') { // Sarah
            const quest = this.questSystem.quests['Q001_MeetAndGreet'];
            if (quest) { // Ensure quest exists
                if (quest.isStarted && quest.stages[0].isCompleted && !quest.stages[1].isCompleted) {
                    this.questSystem.completeObjective('Q001_MeetAndGreet', 1); // Complete "Talk to Sarah"
                    dialogueToShow = ["Oh, Bob sent you? Nice to meet you!", "I guess that's all for now."];
                } else if (quest.isCompleted) {
                    dialogueToShow = ["It was nice meeting you."];
                } else if (!quest.isStarted || !quest.stages[0].isCompleted) {
                    dialogueToShow = ["I'm a bit busy right now, perhaps talk to Bob first?"];
                }
            }
        }

        if (this.scene.dialogueBox && dialogueToShow && dialogueToShow.length > 0) {
            this.scene.dialogueBox.startDialogue(this.npcName, dialogueToShow);
        } else {
            // Fallback if dialogueToShow is empty or no dialogue box
            console.log(`${this.npcName} has nothing specific to say or no dialogue box found.`);
            // Optionally, could show default dialogue if dialogueToShow is empty:
            // if (this.scene.dialogueBox && this.dialogueLines && this.dialogueLines.length > 0) {
            //    this.scene.dialogueBox.startDialogue(this.npcName, this.dialogueLines);
            // }
        }
    }
}

export default NPC;
