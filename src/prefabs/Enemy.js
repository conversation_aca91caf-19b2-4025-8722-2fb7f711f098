// src/prefabs/Enemy.js
import Phaser from 'phaser';
import { SVGAssets } from '../configs/AssetConfig.js';

class Enemy extends Phaser.GameObjects.Sprite {
    constructor(scene, x, y, enemyId, assetKey) {
        super(scene, x, y, assetKey);

        this.enemyId = enemyId;
        this.scene = scene; // Store scene reference for accessing player

        scene.add.existing(this);
        scene.physics.add.existing(this);
        this.setImmovable(true);
        this.setOrigin(0.5, 0.5); // Ensure origin is centered for rotation
        this.rotation = 0; // Explicitly set initial rotation (e.g., 0 for facing right)

        // Set display size using SVGAssets for the given assetKey
        const assetCfg = SVGAssets[assetKey];
        if (assetCfg) {
            this.setDisplaySize(assetCfg.width, assetCfg.height);
        }

        // Vision Cone Setup
        this.visionConeGraphics = scene.add.graphics();
        this.visionConeGraphics.setDepth(this.depth - 0.1); // Draw cone behind enemy slightly
        this.visionConeRange = 150;
        this.visionConeAngle = Math.PI / 4; // 45 degrees FOV (PI/4 is 45 degrees)
        this.visionPolygon = new Phaser.Geom.Polygon();
        this.playerInSight = false;
        this.combatTriggered = false; // Initialize combatTriggered
    }

    preUpdate(time, delta) {
        super.preUpdate(time, delta);
        this.updateVisionConeAndCheckPlayer();
    }

    updateVisionConeAndCheckPlayer() {
        const directionAngle = this.rotation; // Use the sprite's rotation

        // Points for the vision cone polygon
        const p0 = { x: this.x, y: this.y }; // Apex of the cone (enemy's position)
        const p1 = {
            x: this.x + this.visionConeRange * Math.cos(directionAngle - this.visionConeAngle / 2),
            y: this.y + this.visionConeRange * Math.sin(directionAngle - this.visionConeAngle / 2)
        };
        const p2 = {
            x: this.x + this.visionConeRange * Math.cos(directionAngle + this.visionConeAngle / 2),
            y: this.y + this.visionConeRange * Math.sin(directionAngle + this.visionConeAngle / 2)
        };

        this.visionPolygon.setTo([p0, p1, p2]);

        const player = this.scene.player; // Access player from the scene
        if (player) {
            this.playerInSight = this.visionPolygon.contains(player.x, player.y);
        } else {
            this.playerInSight = false;
        }

        if (this.playerInSight) {
            console.log(`Player SEEN by ${this.enemyId}!`);
        }

        // Handle combat trigger and vision cone visibility
        if (this.playerInSight && !this.combatTriggered) {
            this.combatTriggered = true;
            console.log(`Player SEEN by ${this.enemyId}! Initiating combat.`);
            const currentSceneKey = this.scene.scene.key;
            this.scene.scene.pause(currentSceneKey);
            this.scene.scene.launch('CombatScene', {
                player: this.scene.player,
                enemyId: this.enemyId,
                previousSceneKey: currentSceneKey
            });
            this.visionConeGraphics.setVisible(false); // Hide vision cone during combat
        } else if (!this.playerInSight && this.combatTriggered) {
            // Optional: Logic for when player is no longer in sight after combat was triggered.
            // For now, combatTriggered remains true until explicitly reset (e.g., by the scene on resume).
            // this.visionConeGraphics.setVisible(true); // Could make it visible again if needed here
        }

        if (!this.combatTriggered) {
            this.visionConeGraphics.setVisible(true);
            this.visionConeGraphics.clear();
            if (this.playerInSight) { // playerInSight might be true briefly before combat starts
                this.visionConeGraphics.fillStyle(0x00ff00, 0.4); // Green if seen
            } else {
                this.visionConeGraphics.fillStyle(0xff0000, 0.3); // Red if not seen
            }
            this.visionConeGraphics.fillTriangle(p0.x, p0.y, p1.x, p1.y, p2.x, p2.y);
        } else {
            // If combat is triggered, ensure cone is hidden (might be redundant but safe)
            this.visionConeGraphics.setVisible(false);
            this.visionConeGraphics.clear(); // Clear it to be sure
        }
    }
}

export default Enemy;
