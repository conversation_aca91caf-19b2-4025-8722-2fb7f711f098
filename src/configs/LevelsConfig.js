// src/configs/LevelsConfig.js
export const LevelsConfig = {
    Floor1Reception: {
        sceneKey: 'Floor1Reception', // Redundant but good for clarity
        backgroundColor: '#E0E0E0', // Slightly different from current hardcoded
        playerStartPosition: { x: 100, y: 450 }, // Example specific start
        displayName: 'Floor 1: Main Reception' // More descriptive name
    },
    Floor1Office: {
        sceneKey: 'Floor1Office',
        backgroundColor: '#D0D0F0', // Light purple-ish blue
        playerStartPosition: { x: 100, y: 100 },
        displayName: 'Floor 1: Office Cubicles'
    }
    // Future floors/rooms will be added here
};
