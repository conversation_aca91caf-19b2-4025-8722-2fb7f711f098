// src/configs/QuestsConfig.js
export const QuestsConfig = {
    Q001_MeetAndGreet: {
        id: 'Q001_MeetAndGreet',
        title: "Office Introductions",
        stages: [
            {
                objective: "Talk to <PERSON> in Reception.",
                targetNPC: "npc001", // <PERSON>'s ID
                type: "talk",
                isCompleted: false
            },
            {
                objective: "Find and talk to <PERSON> in the Office area.",
                targetNPC: "npc002", // <PERSON>'s ID
                type: "talk",
                isCompleted: false
            }
        ],
        rewards: { experience: 50 }, // Example reward
        isStarted: false,
        isCompleted: false
    }
};
