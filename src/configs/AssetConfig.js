// src/configs/AssetConfig.js
export const SVGAssets = {
    // Player
    player: { path: 'assets/svg/player.svg', width: 50, height: 50 }, // Existing

    // NPCs - Male
    npc_male_1: { path: 'assets/svg/npc_male_1.svg', width: 50, height: 50 },
    npc_male_2: { path: 'assets/svg/npc_male_2.svg', width: 50, height: 50 },
    npc_male_3: { path: 'assets/svg/npc_male_3.svg', width: 50, height: 50 },

    // NPCs - Female
    npc_female_1: { path: 'assets/svg/npc_female_1.svg', width: 50, height: 50 },
    npc_female_2: { path: 'assets/svg/npc_female_2.svg', width: 50, height: 50 },
    npc_female_3: { path: 'assets/svg/npc_female_3.svg', width: 50, height: 50 },

    // NPCs - Specific
    npc_guard: { path: 'assets/svg/npc_guard.svg', width: 55, height: 55 },
    npc_supervisor: { path: 'assets/svg/npc_supervisor.svg', width: 55, height: 55 },

    // Item Icons (10)
    item_key: { path: 'assets/svg/item_key.svg', width: 30, height: 30 },
    item_phone: { path: 'assets/svg/item_phone.svg', width: 30, height: 30 },
    item_document: { path: 'assets/svg/item_document.svg', width: 30, height: 30 },
    item_food: { path: 'assets/svg/item_food.svg', width: 30, height: 30 },
    item_tool: { path: 'assets/svg/item_tool.svg', width: 30, height: 30 },
    item_card: { path: 'assets/svg/item_card.svg', width: 30, height: 30 },
    item_money: { path: 'assets/svg/item_money.svg', width: 30, height: 30 },
    item_medkit: { path: 'assets/svg/item_medkit.svg', width: 30, height: 30 },
    item_book: { path: 'assets/svg/item_book.svg', width: 30, height: 30 },
    item_usb: { path: 'assets/svg/item_usb.svg', width: 30, height: 30 },
    item_memo: { path: 'assets/svg/item_memo.svg', width: 30, height: 30 }, // New memo item

    // Environment Objects (5)
    env_computer: { path: 'assets/svg/env_computer.svg', width: 60, height: 60 },
    env_desk: { path: 'assets/svg/env_desk.svg', width: 100, height: 50 },
    env_chair: { path: 'assets/svg/env_chair.svg', width: 40, height: 60 },
    env_bed: { path: 'assets/svg/env_bed.svg', width: 80, height: 100 },
    env_plant: { path: 'assets/svg/env_plant.svg', width: 40, height: 60 },

    // UI Icons (5)
    ui_close: { path: 'assets/svg/ui_close.svg', width: 24, height: 24 },
    ui_inventory: { path: 'assets/svg/ui_inventory.svg', width: 24, height: 24 },
    ui_map: { path: 'assets/svg/ui_map.svg', width: 24, height: 24 },
    ui_settings: { path: 'assets/svg/ui_settings.svg', width: 24, height: 24 },
    ui_save: { path: 'assets/svg/ui_save.svg', width: 24, height: 24 },

    // Misc (already created in previous plan, confirm paths & sizes)
    enemy: { path: 'assets/svg/enemy.svg', width: 50, height: 50 }, // Existing, can be an alias for npc_guard or a distinct one
    item: { path: 'assets/svg/item.svg', width: 30, height: 30 },   // Existing, can be an alias for one of the item_ icons
    door: { path: 'assets/svg/door.svg', width: 60, height: 100 }  // Existing, Environment object
};
